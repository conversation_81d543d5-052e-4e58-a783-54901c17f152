# SSH私钥权限修复脚本
# 修复AWS001-J001.pem的文件权限

Write-Host "正在修复SSH私钥权限..." -ForegroundColor Green

$keyPath = "C:\Users\<USER>\.ssh\AWS001-J001.pem"

Write-Host "1. 检查当前权限" -ForegroundColor Yellow
icacls $keyPath

Write-Host "2. 禁用继承" -ForegroundColor Yellow
icacls $keyPath /inheritance:d

Write-Host "3. 移除不安全权限" -ForegroundColor Yellow
icacls $keyPath /remove:g "Everyone"
icacls $keyPath /remove:g "Users"
icacls $keyPath /remove:g "Authenticated Users"

Write-Host "4. 添加安全权限" -ForegroundColor Yellow
icacls $keyPath /grant:r "SYSTEM:(F)"
icacls $keyPath /grant:r "lenovo:(F)"
icacls $keyPath /grant:r "Administrators:(F)"

Write-Host "5. 验证最终权限" -ForegroundColor Yellow
icacls $keyPath

Write-Host "权限修复完成！" -ForegroundColor Green
Read-Host "按任意键继续..."
